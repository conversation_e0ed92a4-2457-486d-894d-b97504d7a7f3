package main

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"
)

var buildDate = "unknown"
var commitHash = "unknown"

type upbitMarket struct {
	Market        string `json:"market"`
	KoreanName    string `json:"korean_name"`
	EnglishName   string `json:"english_name"`
	MarketWarning string `json:"market_warning"`
}

type coinAnalysis struct {
	Symbol         string   `json:"symbol"`
	AvailableQuotes []string `json:"available_quotes"`
	HasKRW         bool     `json:"has_krw"`
	HasUSDT        bool     `json:"has_usdt"`
	HasBTC         bool     `json:"has_btc"`
}

type analysisResult struct {
	Timestamp        string         `json:"timestamp"`
	CoinsWithoutKRW  []coinAnalysis `json:"coins_without_krw"`
	TotalCoins       int            `json:"total_coins"`
	CoinsWithoutKRWCount int        `json:"coins_without_krw_count"`
	Summary          string         `json:"summary"`
}

func main() {
	fmt.Println("# analyze-upbit-pairs")
	fmt.Println("Version: 0.0.1")
	fmt.Println("Build: " + buildDate)
	fmt.Println("Commit: " + commitHash)
	fmt.Println("Author: https://t.me/suenot")
	fmt.Println("---")
	fmt.Println("Analyzing Upbit pairs to find coins without KRW market...")

	// Load .env if present
	loadDotEnv()

	client := &http.Client{Timeout: 15 * time.Second}

	// Fetch all markets from Upbit
	markets, err := fetchUpbitMarkets(client)
	if err != nil {
		log.Fatalf("Failed to fetch Upbit markets: %v", err)
	}

	fmt.Printf("Fetched %d markets from Upbit\n", len(markets))

	// Analyze markets
	result := analyzeMarkets(markets)

	// Save result to JSON file
	cwd, _ := os.Getwd()
	outputPath := filepath.Join(cwd, "upbit_pairs_analysis.json")
	
	if err := saveAnalysisResult(outputPath, result); err != nil {
		log.Fatalf("Failed to save analysis result: %v", err)
	}

	fmt.Printf("Analysis complete! Found %d coins without KRW market out of %d total coins\n", 
		result.CoinsWithoutKRWCount, result.TotalCoins)
	fmt.Printf("Results saved to: %s\n", outputPath)

	// Print summary
	fmt.Println("\nCoins without KRW market:")
	for _, coin := range result.CoinsWithoutKRW {
		fmt.Printf("- %s: %v\n", coin.Symbol, coin.AvailableQuotes)
	}
}

func fetchUpbitMarkets(client *http.Client) ([]upbitMarket, error) {
	url := "https://api.upbit.com/v1/market/all?isDetails=false"
	req, _ := http.NewRequest(http.MethodGet, url, nil)
	req.Header.Set("User-Agent", "upbit-listing/1.0")
	
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	
	if res.StatusCode != http.StatusOK {
		b, _ := io.ReadAll(res.Body)
		return nil, fmt.Errorf("upbit %s: %s", res.Status, string(b))
	}
	
	var markets []upbitMarket
	if err := json.NewDecoder(res.Body).Decode(&markets); err != nil {
		return nil, err
	}
	
	return markets, nil
}

func analyzeMarkets(markets []upbitMarket) analysisResult {
	// Group markets by base currency (coin symbol)
	coinMarkets := make(map[string][]string)
	
	for _, market := range markets {
		parts := strings.Split(strings.ToUpper(strings.TrimSpace(market.Market)), "-")
		if len(parts) >= 2 {
			quote := parts[0]  // KRW, USDT, BTC
			base := parts[1]   // Coin symbol
			
			if coinMarkets[base] == nil {
				coinMarkets[base] = []string{}
			}
			coinMarkets[base] = append(coinMarkets[base], quote)
		}
	}
	
	// Find coins without KRW market
	var coinsWithoutKRW []coinAnalysis
	
	for symbol, quotes := range coinMarkets {
		sort.Strings(quotes) // Sort for consistent output
		
		hasKRW := contains(quotes, "KRW")
		hasUSDT := contains(quotes, "USDT")
		hasBTC := contains(quotes, "BTC")
		
		if !hasKRW {
			coinsWithoutKRW = append(coinsWithoutKRW, coinAnalysis{
				Symbol:         symbol,
				AvailableQuotes: quotes,
				HasKRW:         hasKRW,
				HasUSDT:        hasUSDT,
				HasBTC:         hasBTC,
			})
		}
	}
	
	// Sort results by symbol for consistent output
	sort.Slice(coinsWithoutKRW, func(i, j int) bool {
		return coinsWithoutKRW[i].Symbol < coinsWithoutKRW[j].Symbol
	})
	
	return analysisResult{
		Timestamp:           time.Now().UTC().Format(time.RFC3339),
		CoinsWithoutKRW:     coinsWithoutKRW,
		TotalCoins:          len(coinMarkets),
		CoinsWithoutKRWCount: len(coinsWithoutKRW),
		Summary:             fmt.Sprintf("Found %d coins without KRW market out of %d total coins", len(coinsWithoutKRW), len(coinMarkets)),
	}
}

func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func saveAnalysisResult(path string, result analysisResult) error {
	data, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		return err
	}
	
	return atomicWrite(path, append(data, '\n'))
}

func atomicWrite(path string, data []byte) error {
	dir := filepath.Dir(path)
	tmp, err := os.CreateTemp(dir, filepath.Base(path)+".*.tmp")
	if err != nil {
		return err
	}
	defer func() { _ = os.Remove(tmp.Name()) }()
	
	if _, err := tmp.Write(data); err != nil {
		_ = tmp.Close()
		return err
	}
	
	if err := tmp.Sync(); err != nil {
		_ = tmp.Close()
		return err
	}
	
	_ = tmp.Close()
	return os.Rename(tmp.Name(), path)
}

// loadDotEnv loads environment variables from .env if present.
func loadDotEnv() {
	f, err := os.Open(".env")
	if err != nil {
		return
	}
	defer f.Close()
	data, err := io.ReadAll(f)
	if err != nil {
		return
	}
	for _, line := range strings.Split(string(data), "\n") {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}
		if idx := strings.Index(line, "="); idx > 0 {
			key := strings.TrimSpace(line[:idx])
			val := strings.TrimSpace(line[idx+1:])
			if key != "" {
				os.Setenv(key, val)
			}
		}
	}
}
